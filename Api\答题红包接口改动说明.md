# 答题红包接口改动说明

## 改动概述
为支持答题自动发放红包功能，对以下两个接口进行了改动：

## 1. answer-status 接口改动

### 接口地址
`GET /api/UserBatchRecord/{userId}/{batchId}/answer-status`

### 新增返回字段
在原有 `AnswerStatusDto` 基础上新增：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rewardAmount | decimal | 红包金额 |
| rewardStatus | byte | 红包状态：0未发放,1发放成功,2发放失败 |
| rewardStatusText | string | 红包状态描述 |
| answerDetails | string | 答题详情JSON |

### 移除字段
- 移除了 `canAnswer` 字段（与 `hasAnswered` 冲突）

### 响应示例
```json
{
  "code": 200,
  "msg": "答题状态获取成功", 
  "success": true,
  "data": {
    "hasRecord": true,
    "hasAnswered": true,
    "totalQuestions": 5,
    "correctAnswers": 4,
    "correctRate": 80.00,
    "answerTime": "2025-01-15T10:30:00",
    "rewardAmount": 10.00,
    "rewardStatus": 1,
    "rewardStatusText": "发放成功",
    "answerDetails": "[{\"questionId\":1,\"selectedOption\":\"A\",\"isCorrect\":true}]"
  }
}
```

## 2. submit-answer 接口改动

### 接口地址
`POST /api/UserBatchRecord/{userId}/submit-answer`

### 返回类型改动
**原返回**: `Result<bool>`
**新返回**: `Result<AnswerSubmitResponseDto>`

### AnswerSubmitResponseDto 结构
| 字段名 | 类型 | 说明 |
|--------|------|------|
| isCorrect | bool | 是否答题正确（正确率>=50%） |
| correctRate | decimal | 答题正确率 |
| rewardAmount | decimal | 获得的红包金额 |
| rewardStatus | byte | 红包发放状态 |
| message | string | 提示信息 |

### 响应示例
**正确率>=50%，获得红包**：
```json
{
  "code": 200,
  "msg": "答题结果提交成功",
  "success": true,
  "data": {
    "isCorrect": true,
    "correctRate": 80.00,
    "rewardAmount": 10.00,
    "rewardStatus": 1,
    "message": "恭喜您答题正确，已发放红包10.00元！"
  }
}
```

**正确率<50%，未获得红包**：
```json
{
  "code": 200,
  "msg": "答题结果提交成功",
  "success": true,
  "data": {
    "isCorrect": false,
    "correctRate": 40.00,
    "rewardAmount": 0,
    "rewardStatus": 0,
    "message": "答题正确率不足50%，未获得红包。"
  }
}
```

## 3. 自动红包发放逻辑

### 发放条件
1. 答题正确率 >= 50%
2. 用户已完播视频
3. 用户尚未获得红包
4. 批次配置了红包金额

### 红包金额
使用批次配置的 `RewardAmount` 字段

## 4. 前端使用建议

### 提交答题后处理
```javascript
const response = await submitAnswer(userId, answerData);
if (response.success) {
  const result = response.data;
  if (result.isCorrect && result.rewardAmount > 0) {
    // 显示获得红包提示
    showSuccess(`恭喜！正确率${result.correctRate}%，获得红包${result.rewardAmount}元`);
  } else {
    // 显示其他结果
    showMessage(result.message);
  }
}
```

### 查询答题状态处理
```javascript
const response = await getAnswerStatus(userId, batchId);
if (response.success) {
  const status = response.data;
  if (status.hasAnswered) {
    // 渲染已答题界面，包含红包信息
    renderAnsweredUI(status);
  } else {
    // 显示答题界面或提示
    renderQuestionUI();
  }
}
```

## 5. 修改完成状态
✅ AnswerStatusDto 添加红包字段，移除冲突字段
✅ 新增 AnswerSubmitResponseDto
✅ 修改 submit-answer 接口逻辑和返回类型
✅ 修改 answer-status 接口返回内容
✅ 自动红包发放功能
✅ 项目编译通过
