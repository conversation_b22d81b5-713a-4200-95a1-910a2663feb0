# 答题和红包接口改动文档

## 改动概述
为了支持答题自动发放红包功能，对答题相关接口进行了以下改动：

## 1. answer-status 接口改动

### 接口地址
`GET /api/UserBatchRecord/{userId}/{batchId}/answer-status`

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | string | 是 | 用户ID |
| batchId | int | 是 | 批次ID |

### 响应参数改动
在原有 `AnswerStatusDto` 基础上新增以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rewardAmount | decimal | 红包金额 |
| rewardStatus | byte | 红包状态：0未发放,1发放成功,2发放失败 |
| rewardStatusText | string | 红包状态描述 |
| answerDetails | string | 答题详情JSON（用于前端渲染具体答题情况） |

### 完整响应示例
```json
{
  "code": 200,
  "msg": "答题状态获取成功",
  "success": true,
  "data": {
    "hasRecord": true,
    "hasAnswered": true,
    "canAnswer": false,
    "totalQuestions": 5,
    "correctAnswers": 4,
    "correctRate": 80.00,
    "answerTime": "2025-01-15T10:30:00",
    "rewardAmount": 10.00,
    "rewardStatus": 1,
    "rewardStatusText": "发放成功",
    "answerDetails": "[{\"questionId\":1,\"selectedOption\":\"A\",\"isCorrect\":true}]"
  }
}
```

## 2. submit-answer 接口改动

### 接口地址
`POST /api/UserBatchRecord/{userId}/submit-answer`

### 请求参数（无变化）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | string | 是 | 用户ID |
| answerDto | AnswerSubmitDto | 是 | 答题数据 |

#### AnswerSubmitDto 结构
```json
{
  "batchId": 1,
  "totalQuestions": 5,
  "correctAnswers": 4,
  "answerDetails": "[{\"questionId\":1,\"selectedOption\":\"A\",\"isCorrect\":true}]"
}
```

### 响应参数改动
**原返回类型**: `Result<bool>`
**新返回类型**: `Result<AnswerSubmitResponseDto>`

#### AnswerSubmitResponseDto 结构
| 字段名 | 类型 | 说明 |
|--------|------|------|
| isCorrect | bool | 是否答题正确（正确率>=50%） |
| correctRate | decimal | 答题正确率 |
| rewardAmount | decimal | 获得的红包金额 |
| rewardStatus | byte | 红包发放状态：0未发放,1发放成功,2发放失败 |
| message | string | 提示信息 |

### 完整响应示例
```json
{
  "code": 200,
  "msg": "答题结果提交成功",
  "success": true,
  "data": {
    "isCorrect": true,
    "correctRate": 80.00,
    "rewardAmount": 10.00,
    "rewardStatus": 1,
    "message": "恭喜您答题正确，已发放红包10.00元！"
  }
}
```

## 3. 业务逻辑改动

### 红包发放条件
1. 用户必须完播视频
2. 用户答题正确率 >= 50%
3. 用户尚未获得红包
4. 批次配置了红包金额

### 红包金额来源
使用批次配置的 `RedPacketAmount` 字段作为红包金额

### 自动发放逻辑
在 `submit-answer` 接口中，提交答题后自动判断是否满足红包发放条件，如果满足则自动发放红包。

## 4. 前端使用说明

### 获取答题状态
调用 `answer-status` 接口获取用户答题状态，如果 `hasAnswered` 为 `true`，则可以根据返回的答题信息和红包信息进行渲染。

### 提交答题
调用 `submit-answer` 接口提交答题，根据返回的 `AnswerSubmitResponseDto` 显示答题结果和红包发放情况。

## 5. 注意事项
1. 红包发放是在答题提交时自动进行的，无需额外调用红包发放接口
2. 如果红包发放失败，会在数据库中记录失败状态，可通过 `answer-status` 接口查询
3. 答题正确率计算：`correctAnswers / totalQuestions * 100`
4. 只有正确率 >= 50% 才能获得红包

## 6. 测试场景

### 测试场景1：正确率>=50%，自动发放红包
1. 用户完播视频
2. 提交答题：总题数5，正确4题（正确率80%）
3. 期望结果：自动发放红包，返回红包金额和成功状态

### 测试场景2：正确率<50%，不发放红包
1. 用户完播视频
2. 提交答题：总题数5，正确2题（正确率40%）
3. 期望结果：不发放红包，提示正确率不足

### 测试场景3：查询已答题用户状态
1. 用户已完成答题并获得红包
2. 调用 answer-status 接口
3. 期望结果：返回完整的答题信息和红包信息

## 7. 代码修改完成状态
✅ AnswerStatusDto 添加红包相关字段
✅ 新增 AnswerSubmitResponseDto
✅ 修改 UserBatchRecordService.SubmitAnswerAsync 方法
✅ 修改 UserBatchRecordService.GetAnswerStatusAsync 方法
✅ 修改 UserBatchRecordController.SubmitAnswer 接口
✅ 修改兼容性接口
✅ 项目编译通过
